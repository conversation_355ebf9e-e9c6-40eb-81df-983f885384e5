#!/usr/bin/env python3
"""
Simulate Live Keno - Demo version
Mô phỏng kết quả keno real-time để test live predictor
"""

import random
import time
from datetime import datetime, timedelta
from itertools import combinations

class SimulateLiveKeno:
    def __init__(self):
        # Money Management Settings (giống live_keno_predictor.py)
        self.daily_capital = 3_000_000
        self.target_profit = 2_000_000
        self.base_ticket_price = 10_000
        self.min_ticket_price = 10_000
        self.max_ticket_price = 50_000
        self.win_multiplier = 3.2
        self.base_tickets = 5
        self.min_tickets = 5
        self.max_tickets = 10
        self.stop_loss_ratio = 0.2
        
        # Simulation Settings
        self.start_period = 51  # Bắt đầu từ kì 51
        self.end_period = 80    # Kết thúc ở kì 80 (demo)
        self.period_interval = 8  # 8 phút/kì
        
        print("✅ Simulate Live Keno initialized")
        print(f"🎯 Simulation: Kì {self.start_period} → {self.end_period}")
        print(f"⏰ Interval: {self.period_interval} phút/kì")

    def generate_historical_data(self, num_periods=50):
        """Tạo dữ liệu lịch sử"""
        historical_data = []
        base_time = datetime.now().replace(hour=8, minute=0, second=0)
        
        for i in range(num_periods):
            period_time = base_time + timedelta(minutes=i * self.period_interval)
            results = sorted(random.sample(range(1, 81), 20))
            historical_data.append({
                'time': period_time.strftime("%H:%M:%S"),
                'results': results,
                'period': i + 1
            })
        
        return historical_data

    def calculate_dynamic_ticket_price(self, consecutive_losses, consecutive_wins, cumulative_profit, recent_big_win=False):
        """Tính giá vé động"""
        base_price = self.base_ticket_price
        price = base_price
        
        if consecutive_losses >= 6:
            price = base_price * 1.3
        elif consecutive_losses >= 3:
            price = base_price * 1.2
        elif recent_big_win:
            price = base_price * 1.4
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            price = base_price * 1.2
        
        return max(self.min_ticket_price, min(price, self.max_ticket_price))

    def calculate_bet_size(self, current_capital, consecutive_losses, ticket_price):
        """Tính số vé cần mua"""
        base_tickets = self.base_tickets
        
        if consecutive_losses >= 2:
            multiplier = min(1.5 ** consecutive_losses, 3)
            base_tickets = min(int(base_tickets * multiplier), self.max_tickets)
        
        max_affordable = current_capital // ticket_price
        final_tickets = min(base_tickets, max_affordable, self.max_tickets)
        
        return max(final_tickets, self.min_tickets) if final_tickets >= self.min_tickets else 0

    def calculate_profit(self, tickets_bought, winning_tickets, ticket_price):
        """Tính lợi nhuận"""
        cost = tickets_bought * ticket_price
        revenue = winning_tickets * ticket_price * self.win_multiplier
        profit = revenue - cost
        return profit, cost, revenue

    def predict_period(self, historical_data):
        """Tạo prediction đơn giản"""
        # Lấy các số xuất hiện nhiều nhất
        all_numbers = []
        for data in historical_data[-20:]:  # 20 kì gần nhất
            all_numbers.extend(data['results'])
        
        from collections import Counter
        frequency = Counter(all_numbers)
        most_common = frequency.most_common(10)
        
        # Tạo Final 7 Strategy
        final_7 = [num for num, _ in most_common[:7]]
        while len(final_7) < 7:
            final_7.append(random.randint(1, 80))
        
        # Tạo random combos
        combos = list(combinations(final_7, 4))
        selected_combos = random.sample(combos, min(15, len(combos)))
        
        return {
            'final_7_strategy': final_7,
            'selected_combos': selected_combos,
            'total_combos': len(selected_combos)
        }

    def simulate_period_result(self):
        """Mô phỏng kết quả một kì"""
        return sorted(random.sample(range(1, 81), 20))

    def run_simulation(self):
        """Chạy simulation"""
        print(f"\n🚀 BẮT ĐẦU SIMULATION - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # Tạo dữ liệu lịch sử
        historical_data = self.generate_historical_data(self.start_period - 1)
        
        # Khởi tạo trạng thái
        current_capital = self.daily_capital
        daily_profit = 0
        consecutive_losses = 0
        consecutive_wins = 0
        recent_big_win = False
        last_period_profit = 0
        total_bets = 0
        
        # Simulation loop
        for period in range(self.start_period, self.end_period + 1):
            print(f"\n🎯 KÌ {period} - {datetime.now().strftime('%H:%M:%S')}")
            
            # Kiểm tra điều kiện dừng
            if daily_profit >= self.target_profit:
                print(f"🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                break
            
            if current_capital <= self.daily_capital * self.stop_loss_ratio:
                print(f"🛑 STOP LOSS! Vốn còn: {current_capital:,} VNĐ - DỪNG CHƠI")
                break
            
            # Tạo prediction
            prediction = self.predict_period(historical_data)
            
            # Detect big win
            recent_big_win = last_period_profit > 100_000
            
            # Tính giá vé và số vé
            ticket_price = self.calculate_dynamic_ticket_price(consecutive_losses, consecutive_wins, daily_profit, recent_big_win)
            tickets_to_buy = self.calculate_bet_size(current_capital, consecutive_losses, ticket_price)
            
            if tickets_to_buy == 0:
                print("💸 Không đủ vốn để mua vé - DỪNG CHƠI")
                break
            
            # Hiển thị prediction
            print(f"   🎯 Final 7: {' '.join([f'{num:2d}' for num in prediction['final_7_strategy']])}")
            print(f"   💰 Mua {tickets_to_buy} vé @ {ticket_price:,} VNĐ/vé")
            
            # Hiển thị pricing logic
            price_status = ""
            if ticket_price > self.base_ticket_price:
                price_increase = ((ticket_price - self.base_ticket_price) / self.base_ticket_price) * 100
                if price_increase >= 40:
                    price_status = f"📈 Tăng giá {price_increase:.0f}% (thắng đậm - aggressive)"
                elif price_increase >= 30:
                    price_status = f"📈 Tăng giá {price_increase:.0f}% (thua 6 kì liên tiếp)"
                elif price_increase >= 20:
                    if consecutive_wins >= 2:
                        price_status = f"📈 Tăng giá {price_increase:.0f}% (đang lãi - aggressive)"
                    else:
                        price_status = f"📈 Tăng giá {price_increase:.0f}% (thua 3 kì liên tiếp)"
            else:
                price_status = "📊 Giá cơ bản"
            print(f"   🎫 {price_status}")
            
            # Mô phỏng kết quả
            actual_results = self.simulate_period_result()
            actual_missing = [i for i in range(1, 81) if i not in actual_results]
            
            # Tính số vé thắng
            winning_tickets = 0
            for combo in prediction['selected_combos']:
                missing_count = sum(1 for num in combo if num in actual_missing)
                if missing_count >= 4:
                    winning_tickets += 1
            
            # Tính lợi nhuận
            period_profit, period_cost, period_revenue = self.calculate_profit(tickets_to_buy, winning_tickets, ticket_price)
            
            # Cập nhật trạng thái
            current_capital += period_profit
            daily_profit += period_profit
            total_bets += 1
            
            # Cập nhật consecutive wins/losses
            last_period_profit = period_profit
            if period_profit > 0:
                consecutive_losses = 0
                consecutive_wins += 1
            else:
                consecutive_losses += 1
                consecutive_wins = 0
            
            # Hiển thị kết quả
            profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
            print(f"   📊 Kết quả: {actual_results}")
            print(f"   💰 {tickets_to_buy} vé → {winning_tickets} vé thắng → {profit_icon} {period_profit:+,} VNĐ")
            print(f"   💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")
            print(f"   📈 Lợi nhuận tích lũy: {daily_profit:+,} VNĐ")
            print(f"   🎯 Tiến độ: {(daily_profit/self.target_profit)*100:.1f}%")
            
            if consecutive_losses > 0:
                print(f"   ⚠️ Thua liên tiếp: {consecutive_losses} lần")
            if consecutive_wins > 1:
                print(f"   🔥 Thắng liên tiếp: {consecutive_wins} lần")
            
            # Thêm kết quả vào historical data
            period_time = datetime.now().replace(hour=8, minute=0, second=0) + timedelta(minutes=(period-1) * self.period_interval)
            historical_data.append({
                'time': period_time.strftime("%H:%M:%S"),
                'results': actual_results,
                'period': period
            })
            
            # Delay để mô phỏng real-time (optional)
            # time.sleep(1)
        
        # Kết thúc simulation
        print(f"\n🏁 KẾT THÚC SIMULATION")
        print("="*80)
        total_cost = self.daily_capital - current_capital + daily_profit
        print(f"💰 Tổng chi phí: {total_cost:,.0f} VNĐ")
        print(f"📈 Lợi nhuận cuối: {daily_profit:+,} VNĐ")
        if total_cost > 0:
            print(f"📊 ROI: {(daily_profit/total_cost)*100:+.1f}%")
        print(f"🎯 Đạt mục tiêu: {(daily_profit/self.target_profit)*100:.1f}%")
        print(f"🎲 Tổng số lần chơi: {total_bets}")

if __name__ == "__main__":
    simulator = SimulateLiveKeno()
    simulator.run_simulation()
