#!/usr/bin/env python3
"""
Test script để verify logic Combined = Least - Excluded
"""

def test_combined_logic():
    """Test logic Combined = Least Frequent - Excluded"""
    
    # Test case 1: Có overlap giữa Least và Excluded
    least_frequent = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45]
    excluded = [10, 20, 30, 50, 60]
    
    # Logic: Least - Excluded
    combined = [num for num in least_frequent if num not in excluded]
    print("Test case 1:")
    print(f"Least Frequent: {least_frequent}")
    print(f"Excluded: {excluded}")
    print(f"Combined (Least - Excluded): {combined}")
    print(f"Expected: [1, 5, 15, 25, 35, 40, 45] (loại bỏ 10, 20, 30)")
    print()
    
    # Test case 2: Không có overlap
    least_frequent = [1, 5, 15, 25, 35, 40, 45, 50, 55, 60]
    excluded = [10, 20, 30, 70, 80]
    
    combined = [num for num in least_frequent if num not in excluded]
    print("Test case 2:")
    print(f"Least Frequent: {least_frequent}")
    print(f"Excluded: {excluded}")
    print(f"Combined (Least - Excluded): {combined}")
    print(f"Expected: {least_frequent} (không loại bỏ gì)")
    print()
    
    # Test case 3: Tất cả Least đều bị Excluded
    least_frequent = [10, 20, 30, 40, 50]
    excluded = [10, 20, 30, 40, 50, 60, 70]
    
    combined = [num for num in least_frequent if num not in excluded]
    print("Test case 3:")
    print(f"Least Frequent: {least_frequent}")
    print(f"Excluded: {excluded}")
    print(f"Combined (Least - Excluded): {combined}")
    print(f"Expected: [] (tất cả bị loại bỏ)")

if __name__ == "__main__":
    test_combined_logic()
