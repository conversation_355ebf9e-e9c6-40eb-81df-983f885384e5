#!/usr/bin/env python3
"""
Live Keno Predictor - Real-time Version
Đ<PERSON>h kết quả ngày hôm nay với real-time monitoring
"""

import mysql.connector
import random
import time
from datetime import datetime, timedelta
from itertools import combinations
from collections import Counter

def connect_db():
    """Kết nối database"""
    return mysql.connector.connect(
        host='localhost',
        port=3307,
        user='root',
        password='1',
        database='keno'
    )

class LiveKenoPredictor:
    def __init__(self):
        # Money Management Settings
        self.daily_capital = 3_000_000  # 3 triệu VNĐ/ngày
        self.target_profit = 2_000_000  # 2 triệu VNĐ/ngày
        
        # Dynamic Pricing Settings
        self.base_ticket_price = 10_000     # Giá vé cơ bản 10k VNĐ
        self.min_ticket_price = 10_000      # Giá vé tối thiểu
        self.max_ticket_price = 50_000      # Gi<PERSON> vé tối đa
        self.win_multiplier = 3.2           # Tỷ lệ thắng 3.2x
        
        # Betting Settings
        self.base_tickets = 5               # Số vé cơ bản mỗi lần
        self.min_tickets = 5                # Tối thiểu 5 vé/lần
        self.max_tickets = 15               # Tối đa 15 vé/lần (tăng để hỗ trợ martingale)
        self.stop_loss_ratio = 0.2          # Dừng khi còn 20% vốn
        self.martingale_trigger = 3         # Bắt đầu martingale từ 3 ván thua
        
        # Live Trading Settings (có thể override từ command line)
        self.min_periods = 50               # Bắt đầu từ kì 51 (default)
        self.end_time = "21:44:00"          # Kì cuối cùng
        self.check_interval = 60            # Check mỗi 60 giây
        
        print("✅ Live Keno Predictor initialized")
        print(f"💰 Vốn/ngày: {self.daily_capital:,} VNĐ")
        print(f"🎯 Mục tiêu: {self.target_profit:,} VNĐ/ngày")
        print(f"🎫 Giá vé: {self.min_ticket_price:,} - {self.max_ticket_price:,} VNĐ (dynamic)")
        print(f"🏆 Tỷ lệ thắng: {self.win_multiplier}x")
        print(f"📊 Số vé: {self.min_tickets}-{self.max_tickets} vé/lần")
        print(f"⏰ Thời gian kết thúc: {self.end_time}")
        print(f"🎮 Bắt đầu từ kì: {self.min_periods + 1}")

    def set_min_periods(self, min_periods):
        """Set minimum periods từ command line"""
        self.min_periods = min_periods
        print(f"⚙️ Cập nhật min_periods: {min_periods} (bắt đầu từ kì {min_periods + 1})")

    def get_today_draws(self):
        """Lấy tất cả kì của ngày hôm nay"""
        try:
            today = datetime.now().strftime("%Y-%m-%d")
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (today,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results từ string thành list và period thành int
            for row in rows:
                if isinstance(row['results'], str):
                    row['results'] = [int(n) for n in row['results'].split(',')]
                # Đảm bảo period là integer
                if isinstance(row['period'], str):
                    try:
                        row['period'] = int(row['period'])
                    except ValueError:
                        row['period'] = 0

            return rows, today

        except Exception as e:
            print(f"❌ Error loading today draws: {e}")
            print("🔄 Sử dụng dữ liệu demo...")
            return self._generate_demo_data(), datetime.now().strftime("%Y-%m-%d")

    def _generate_demo_data(self):
        """Tạo dữ liệu demo cho live trading"""
        demo_data = []
        current_time = datetime.now()

        # Tạo 55 kì (từ 08:00 đến hiện tại)
        for i in range(55):
            period_time = current_time.replace(hour=8, minute=0, second=0) + timedelta(minutes=i*8)
            if period_time <= current_time:
                results = sorted(random.sample(range(1, 81), 20))
                demo_data.append({
                    'time': period_time.strftime("%H:%M:%S"),
                    'results': results,
                    'period': i + 1
                })

        return demo_data

    def get_latest_period_number(self, draws):
        """Lấy số kì mới nhất"""
        if not draws:
            return 0
        # Đảm bảo period là integer
        periods = []
        for draw in draws:
            try:
                period = int(draw['period']) if isinstance(draw['period'], str) else draw['period']
                periods.append(period)
            except (ValueError, TypeError):
                # Nếu period không phải số, bỏ qua
                continue

        return max(periods) if periods else 0

    def wait_for_new_period(self, current_period):
        """Chờ kì mới"""
        print(f"⏳ Chờ kì {current_period + 1}... (check mỗi {self.check_interval}s)")
        
        while True:
            time.sleep(self.check_interval)
            draws, _ = self.get_today_draws()
            latest_period = self.get_latest_period_number(draws)
            
            current_time = datetime.now().strftime("%H:%M:%S")
            print(f"🕐 {current_time} - Latest period: {latest_period}")
            
            if latest_period > current_period:
                print(f"🆕 Kì {latest_period} đã có kết quả!")
                return draws
            
            # Kiểm tra nếu đã qua giờ kết thúc
            if current_time >= self.end_time:
                print(f"⏰ Đã qua giờ kết thúc ({self.end_time})")
                return None

    def calculate_dynamic_ticket_price(self, consecutive_losses, consecutive_wins, cumulative_profit, recent_big_win=False):
        """Tính giá vé động dựa trên tình hình thắng/thua với chiến thuật mới"""
        base_price = self.base_ticket_price
        price = base_price

        # CHIẾN THUẬT MỚI: Tăng giá vé khi thua liên tiếp
        if consecutive_losses >= 6:
            # Thua 6 kì liên tiếp → tăng giá vé 300%
            price = base_price * 3.0
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng giá vé 200%
            price = base_price * 2.0

        # Logic tăng giá khi thắng (aggressive betting)
        elif recent_big_win:
            price = base_price * 1.4  # Thắng đậm → tăng 40%
        elif consecutive_wins >= 2 and cumulative_profit > 0:
            price = base_price * 1.2  # Đang lãi → tăng 20%

        # Cho phép vượt max_ticket_price trong trường hợp martingale
        max_allowed_price = self.max_ticket_price * 3 if consecutive_losses >= 3 else self.max_ticket_price
        return max(self.min_ticket_price, min(price, max_allowed_price))

    def calculate_bet_size(self, current_capital, consecutive_losses, win_rate, ticket_price):
        """Tính số vé cần mua với chiến thuật mới"""
        base_tickets = self.base_tickets

        # Điều chỉnh dựa trên win rate
        if win_rate >= 0.40:
            base_tickets = self.base_tickets
        elif win_rate >= 0.35:
            base_tickets = max(self.base_tickets - 2, self.min_tickets)
        elif win_rate >= 0.30:
            base_tickets = max(self.base_tickets - 3, self.min_tickets)
        else:
            base_tickets = self.min_tickets

        # CHIẾN THUẬT MỚI: Tăng cược khi thua liên tiếp
        if consecutive_losses >= 6:
            # Thua 6 kì liên tiếp → tăng 300% (đánh liên tiếp 6 kì)
            base_tickets = int(base_tickets * 3.0)
            print(f"      🔥 MARTINGALE 300%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x3 + GIÁ VÉ x3")
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng 200% (đánh liên tiếp 3 kì)
            base_tickets = int(base_tickets * 2.0)
            print(f"      📈 MARTINGALE 200%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2")

        # Giới hạn theo vốn và max tickets
        max_affordable = current_capital // ticket_price
        final_tickets = min(base_tickets, max_affordable, self.max_tickets * 2)  # Cho phép vượt max_tickets trong trường hợp martingale

        return max(final_tickets, self.min_tickets) if final_tickets >= self.min_tickets else 0

    def calculate_profit(self, tickets_bought, winning_tickets, ticket_price):
        """Tính lợi nhuận"""
        cost = tickets_bought * ticket_price
        revenue = winning_tickets * ticket_price * self.win_multiplier
        profit = revenue - cost
        return profit, cost, revenue

    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Dự đoán LSTM dựa trên frequency"""
        try:
            all_numbers = []
            for result in day_results[-5000:]:
                all_numbers.extend(result['results'])
            
            frequency = Counter(all_numbers)
            most_common = frequency.most_common(num_predictions)
            return [num for num, _ in most_common]
        except:
            return list(range(1, num_predictions + 1))

    def get_seasonal_patterns(self, day_results, num_predictions=10):
        """Phân tích seasonal patterns"""
        try:
            seasonal_numbers = []
            for result in day_results[-1000:]:
                seasonal_numbers.extend(result['results'])
            
            frequency = Counter(seasonal_numbers)
            most_common = frequency.most_common(num_predictions)
            return [num for num, _ in most_common]
        except:
            return list(range(1, num_predictions + 1))

    def get_excluded_numbers(self, day_results):
        """Lấy các số bị loại trừ"""
        try:
            if not day_results:
                return set()
            
            recent_results = day_results[-10:]
            excluded = set()
            
            for result in recent_results:
                numbers = result['results']
                for i in range(len(numbers) - 1):
                    if numbers[i+1] - numbers[i] == 1:
                        excluded.add(numbers[i])
                        excluded.add(numbers[i+1])
            
            return excluded
        except:
            return set()

    def predict_next_period(self, day_draws):
        """Dự đoán kì tiếp theo"""
        if len(day_draws) < 10:
            return None
        
        # Training data (tất cả kì trước đó)
        training_data = [draw['results'] for draw in day_draws]
        
        # 1. Top 1-3 từ LSTM Original (3 số)
        lstm_predictions = self.get_lstm_predictions(day_draws, 10)
        final_7_strategy = lstm_predictions[:3]
        
        # 2. Top 2 từ Seasonal Original (1 số)
        seasonal_predictions = self.get_seasonal_patterns(day_draws, 10)
        if len(seasonal_predictions) >= 2:
            final_7_strategy.append(seasonal_predictions[1])  # Top2
        
        # 3. Top 1-3 từ Excluded (3 số)
        excluded_numbers = list(self.get_excluded_numbers(day_draws))
        if len(excluded_numbers) >= 3:
            final_7_strategy.extend(excluded_numbers[:3])
        
        # Đảm bảo có đúng 7 số
        while len(final_7_strategy) < 7:
            for num in range(1, 81):
                if num not in final_7_strategy:
                    final_7_strategy.append(num)
                    if len(final_7_strategy) >= 7:
                        break
        
        final_7_strategy = final_7_strategy[:7]
        
        # Tạo ngẫu nhiên 10-20 cặp 4 số từ 7 số
        if len(final_7_strategy) >= 4:
            all_possible_combos = list(combinations(final_7_strategy, 4))
            num_combos = min(random.randint(10, 20), len(all_possible_combos))
            selected_combos = random.sample(all_possible_combos, num_combos)
        else:
            selected_combos = []
        
        return {
            'final_7_strategy': final_7_strategy,
            'selected_combos': selected_combos,
            'total_combos': len(selected_combos)
        }

    def start_live_trading(self):
        """Bắt đầu live trading"""
        print(f"\n🚀 BẮT ĐẦU LIVE TRADING - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # Khởi tạo trạng thái
        current_capital = self.daily_capital
        daily_profit = 0
        consecutive_losses = 0
        consecutive_wins = 0
        recent_big_win = False
        last_period_profit = 0
        total_bets = 0
        martingale_streak = 0  # Theo dõi streak cho martingale
        
        # Estimated win rate
        estimated_win_rate = 0.36
        
        while True:
            # Lấy dữ liệu hiện tại
            draws, today = self.get_today_draws()
            current_period = self.get_latest_period_number(draws)
            
            print(f"\n📊 Hiện tại: {len(draws)} kì, kì mới nhất: {current_period}")
            
            # Kiểm tra điều kiện dừng
            if daily_profit >= self.target_profit:
                print(f"🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                break
            
            if current_capital <= self.daily_capital * self.stop_loss_ratio:
                print(f"🛑 STOP LOSS! Vốn còn: {current_capital:,} VNĐ - DỪNG CHƠI")
                break
            
            current_time = datetime.now().strftime("%H:%M:%S")
            if current_time >= self.end_time:
                print(f"⏰ Đã đến giờ kết thúc ({self.end_time}) - DỪNG CHƠI")
                break
            
            # Chỉ trade từ kì 51 trở đi
            if current_period < self.min_periods:
                print(f"⏳ Chờ đến kì {self.min_periods + 1}...")
                draws = self.wait_for_new_period(current_period)
                if draws is None:
                    break
                continue
            
            # Dự đoán kì tiếp theo
            prediction = self.predict_next_period(draws)
            if not prediction:
                print("❌ Không thể tạo prediction")
                break
            
            # Detect big win
            recent_big_win = last_period_profit > 100_000
            
            # Tính giá vé và số vé
            ticket_price = self.calculate_dynamic_ticket_price(consecutive_losses, consecutive_wins, daily_profit, recent_big_win)
            tickets_to_buy = self.calculate_bet_size(current_capital, consecutive_losses, estimated_win_rate, ticket_price)
            
            if tickets_to_buy == 0:
                print("💸 Không đủ vốn để mua vé - DỪNG CHƠI")
                break
            
            # Hiển thị prediction
            next_period = current_period + 1
            print(f"\n🎯 PREDICTION CHO KÌ {next_period}")
            print(f"   📊 Final 7: {' '.join([f'{num:2d}' for num in prediction['final_7_strategy']])}")
            print(f"   💰 Sẽ mua: {tickets_to_buy} vé @ {ticket_price:,} VNĐ/vé")
            print(f"   💳 Vốn hiện tại: {current_capital:,} VNĐ")
            print(f"   📈 Lợi nhuận tích lũy: {daily_profit:+,} VNĐ")

            # Hiển thị các vé 4 số cụ thể sẽ mua
            print(f"   🎫 CÁC VÉ 4 SỐ SẼ MUA ({len(prediction['selected_combos'])} vé có sẵn):")
            tickets_to_display = min(tickets_to_buy, len(prediction['selected_combos']))
            actual_tickets_bought = prediction['selected_combos'][:tickets_to_display]

            for i, combo in enumerate(actual_tickets_bought, 1):
                print(f"      Vé {i:2d}: {' '.join([f'{num:2d}' for num in sorted(combo)])}")

            if tickets_to_display < len(prediction['selected_combos']):
                print(f"      ... và {len(prediction['selected_combos']) - tickets_to_display} vé khác (không mua)")
            
            # Chờ kết quả kì tiếp theo
            draws = self.wait_for_new_period(current_period)
            if draws is None:
                break
            
            # Lấy kết quả kì vừa ra
            latest_draw = max(draws, key=lambda x: x['period'])
            actual_results = latest_draw['results']
            actual_missing = [i for i in range(1, 81) if i not in actual_results]

            # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA
            tickets_to_display = min(tickets_to_buy, len(prediction['selected_combos']))
            actual_tickets_bought = prediction['selected_combos'][:tickets_to_display]

            winning_tickets = 0
            winning_details = []
            for i, combo in enumerate(actual_tickets_bought, 1):
                missing_count = sum(1 for num in combo if num in actual_missing)
                is_winning = missing_count >= 4
                if is_winning:
                    winning_tickets += 1
                winning_details.append({
                    'ticket_num': i,
                    'combo': combo,
                    'missing_count': missing_count,
                    'is_winning': is_winning
                })

            # Tính lợi nhuận dựa trên số vé thực tế mua
            period_profit, period_cost, period_revenue = self.calculate_profit(tickets_to_display, winning_tickets, ticket_price)
            
            # Cập nhật trạng thái
            current_capital += period_profit
            daily_profit += period_profit
            total_bets += 1
            
            # Cập nhật consecutive wins/losses
            last_period_profit = period_profit
            if period_profit > 0:
                consecutive_losses = 0
                consecutive_wins += 1
                martingale_streak = 0  # Reset martingale streak khi thắng
            else:
                consecutive_losses += 1
                consecutive_wins = 0
                martingale_streak += 1  # Tăng martingale streak khi thua
            
            # Hiển thị kết quả chi tiết
            profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
            print(f"\n✅ KẾT QUẢ KÌ {latest_draw['period']} - {latest_draw['time']}")
            print(f"   📊 Kết quả: {actual_results}")
            print(f"   � Số trượt: {sorted(actual_missing)}")

            # Hiển thị chi tiết từng vé
            print(f"   🎫 CHI TIẾT CÁC VÉ ĐÃ MUA:")
            for detail in winning_details:
                status_icon = "✅" if detail['is_winning'] else "❌"
                combo_str = ' '.join([f'{num:2d}' for num in sorted(detail['combo'])])
                print(f"      Vé {detail['ticket_num']:2d}: {combo_str} → {detail['missing_count']}/4 trượt {status_icon}")

            # Tổng kết tài chính
            print(f"   �💰 Chi phí: {tickets_to_display} vé × {ticket_price:,} = {period_cost:,} VNĐ")
            print(f"   💰 Thu nhập: {winning_tickets} vé × {ticket_price:,} × {self.win_multiplier} = {period_revenue:,} VNĐ")
            print(f"   💰 Lợi nhuận: {profit_icon} {period_profit:+,} VNĐ")
            print(f"   💳 Vốn còn: {current_capital:,} VNĐ")
            print(f"   📈 Lợi nhuận tích lũy: {daily_profit:+,} VNĐ")
            print(f"   🎯 Tiến độ: {(daily_profit/self.target_profit)*100:.1f}%")
            
            if consecutive_losses > 0:
                print(f"   ⚠️ Thua liên tiếp: {consecutive_losses} lần")
            if consecutive_wins > 1:
                print(f"   🔥 Thắng liên tiếp: {consecutive_wins} lần")
        
        # Kết thúc session
        print(f"\n🏁 KẾT THÚC LIVE TRADING SESSION")
        print("="*80)
        total_cost = self.daily_capital - current_capital + daily_profit
        print(f"💰 Vốn ban đầu: {self.daily_capital:,} VNĐ")
        print(f"💰 Vốn còn lại: {current_capital:,} VNĐ")
        print(f"💰 Tổng chi phí: {total_cost:,.0f} VNĐ")
        print(f"📈 Lợi nhuận cuối: {daily_profit:+,} VNĐ")
        if total_cost > 0:
            print(f"📊 ROI: {(daily_profit/total_cost)*100:+.1f}%")
        print(f"🎯 Đạt mục tiêu: {(daily_profit/self.target_profit)*100:.1f}%")
        print(f"🎲 Tổng số lần chơi: {total_bets}")

def start_live_trading_with_params(min_periods=50):
    """Function để start live trading với parameters"""
    predictor = LiveKenoPredictor()
    if min_periods != 50:
        predictor.set_min_periods(min_periods)
    return predictor.start_live_trading()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 2:
        print("Usage: python live_keno_predictor.py [MIN_PERIODS]")
        print("Example: python live_keno_predictor.py")
        print("Example: python live_keno_predictor.py 30")
        sys.exit(1)

    min_periods = int(sys.argv[1]) if len(sys.argv) == 2 else 50
    start_live_trading_with_params(min_periods)
