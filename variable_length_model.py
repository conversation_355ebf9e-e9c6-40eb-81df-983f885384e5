import tensorflow as tf
import numpy as np
import pandas as pd
import mysql.connector
from sklearn.model_selection import train_test_split

# Hàm in thông báo
def print_header(message):
    print(f"\n\033[1;36m{'='*20} {message} {'='*20}\033[0m")

def print_success(message):
    print(f"\033[1;32m✓ {message}\033[0m")

def print_info(message):
    pass  # Tắt logs để không làm rối output

def print_warning(message):
    print(f"\033[1;33m⚠ {message}\033[0m")

# Kết nối database
def connect_db():
    return mysql.connector.connect(
        host="localhost",
        port=3307,
        user="root",
        password="1",
        database="keno"
    )

class VariableLengthKenoModel:
    """
    Model xử lý variable length sequences cho Keno

    Logic ĐÚNG:
    - Mỗi ngày có 119 kỳ (6:00:00 - 21:44:00, mỗi 8 phút)
    - Kỳ 55: Dùng TẤT CẢ 54 kỳ trước đó để dự đoán
    - Kỳ 119: Dùng TẤT CẢ 118 kỳ trước đó để dự đoán
    """

    def __init__(self, max_sequence_length=150):
        self.max_sequence_length = max_sequence_length  # Đủ cho 119 kỳ + buffer
        self.model = None
        self.draws_per_day = 119  # Confirm: 119 kỳ/ngày

    def create_variable_length_features(self, df):
        """Tạo features với variable length"""
        print_header("TẠO VARIABLE LENGTH FEATURES")
        print_info("🎯 Logic: Dùng TẤT CẢ kỳ trước đó để dự đoán kỳ tiếp theo")

        dates = df['date'].unique()
        all_sequences = []
        all_targets = []
        all_lengths = []  # Lưu độ dài thực của mỗi sequence

        valid_dates = 0

        for date in sorted(dates):
            day_data = df[df['date'] == date].copy().reset_index(drop=True)

            if len(day_data) < 50:  # Cần ít nhất 50 kỳ
                continue

            valid_dates += 1
            day_sequences = 0

            # Tạo ma trận nhị phân cho ngày này
            numbers_matrix = np.zeros((len(day_data), 80))
            for i, numbers in enumerate(day_data['results']):
                for num in numbers:
                    if 1 <= num <= 80:
                        numbers_matrix[i, num-1] = 1

            # Tạo sequences với variable length - LOGIC ĐÚNG
            for i in range(50, len(numbers_matrix)):  # Bắt đầu từ kỳ 51 (cần ≥50 kỳ)
                # Dùng TẤT CẢ kỳ trước đó (từ kỳ 1 đến kỳ i)
                seq = numbers_matrix[0:i]  # Tất cả kỳ từ đầu ngày
                actual_length = len(seq)

                # Xử lý sequence length với RIGHT-PADDING (cuDNN compatible)
                if actual_length <= self.max_sequence_length:
                    # Right-pad với zeros (data ở đầu, zeros ở cuối)
                    padded_seq = np.zeros((self.max_sequence_length, 80))
                    padded_seq[:actual_length] = seq  # Đặt data vào đầu
                    all_sequences.append(padded_seq)
                    all_lengths.append(actual_length)
                else:
                    # Nếu vượt quá max_length (không nên xảy ra với 119 kỳ)
                    print_warning(f"Sequence quá dài: {actual_length} > {self.max_sequence_length}")
                    truncated_seq = seq[-self.max_sequence_length:]
                    all_sequences.append(truncated_seq)
                    all_lengths.append(self.max_sequence_length)

                target = numbers_matrix[i]  # Kỳ tiếp theo cần dự đoán
                all_targets.append(target)
                day_sequences += 1

            if valid_dates <= 3:
                print_info(f"  📅 Ngày {date}: {len(day_data)} kỳ → {day_sequences} sequences")
                print_info(f"      Logic: Kỳ 51 dùng 50 kỳ, Kỳ 80 dùng 79 kỳ, Kỳ 119 dùng 118 kỳ")
                if len(day_data) >= 119:
                    print_info(f"      ✅ Ngày đầy đủ: {len(day_data)} kỳ")

        print_success(f"✅ Xử lý {valid_dates} ngày")
        print_info(f"📊 Tổng: {len(all_sequences):,} sequences")
        print_info(f"📏 Độ dài trung bình: {np.mean(all_lengths):.1f} kỳ")

        return np.array(all_sequences), np.array(all_targets), np.array(all_lengths)

    def create_model_with_masking(self):
        """Tạo model với masking để xử lý variable length - GIỮ CHẤT LƯỢNG"""
        print_header("TẠO MODEL VỚI MASKING - CHẤT LƯỢNG CAO")

        model = tf.keras.Sequential([
            # Input layer
            tf.keras.layers.Input(shape=(self.max_sequence_length, 80)),

            # Masking layer để ignore padded values (zeros)
            tf.keras.layers.Masking(mask_value=0.0),

            # LSTM layers - GIỮ NGUYÊN CHẤT LƯỢNG
            tf.keras.layers.LSTM(64, return_sequences=True, dropout=0.2, use_cudnn=False),
            tf.keras.layers.LSTM(32, dropout=0.2, use_cudnn=False),

            # Dense layers - GIỮ NGUYÊN
            tf.keras.layers.Dense(32, activation='relu'),
            tf.keras.layers.Dropout(0.3),
            tf.keras.layers.Dense(80, activation='sigmoid')
        ])

        # Optimizer với learning rate hơi cao hơn (tối ưu nhẹ)
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.0015),  # Tăng nhẹ từ 0.001->0.0015
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        print_success("✅ Model chất lượng cao đã được tạo")
        print_info(f"📏 Max sequence length: {self.max_sequence_length}")
        print_info("🎯 Ưu tiên: Giữ chất lượng model, tối ưu nhẹ learning rate")
        return model

    def train_variable_model(self, X, y, lengths, epochs=50, batch_size=32):
        """Train model với variable length data - Tối ưu cho Apple Silicon"""
        print_header("TRAINING VARIABLE LENGTH MODEL - APPLE SILICON OPTIMIZED")

        # Kiểm tra và cấu hình Apple Silicon GPU (MPS)
        print_info("🔍 Kiểm tra Apple Silicon GPU (MPS):")

        # Kiểm tra MPS availability
        if hasattr(tf.config.experimental, 'list_physical_devices'):
            try:
                # Kiểm tra MPS
                if tf.config.list_physical_devices('GPU'):
                    print_success("✅ Phát hiện Apple Silicon GPU")
                    print_info("🍎 MacBook Pro M4 Pro - 16-Core GPU, 24GB Unified Memory")

                    # Cấu hình memory growth cho Apple Silicon
                    gpus = tf.config.list_physical_devices('GPU')
                    if gpus:
                        try:
                            for gpu in gpus:
                                print_success(f"GPU:  {gpu.name}")
                                tf.config.experimental.set_memory_growth(gpu, True)
                            print_success("✅ Đã cấu hình GPU memory growth cho Apple Silicon")
                        except RuntimeError as e:
                            print_warning(f"GPU memory config: {e}")

                    device_name = '/GPU:0'
                    print_info("🚀 Sử dụng Apple Silicon GPU với MPS backend")
                else:
                    device_name = '/CPU:0'
                    print_warning("⚠️ Fallback to CPU")
            except:
                device_name = '/CPU:0'
                print_warning("⚠️ Không thể detect GPU, sử dụng CPU")
        else:
            device_name = '/CPU:0'

        # Chia dữ liệu
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        print_info(f"Train: {len(X_train):,}, Test: {len(X_test):,}")

        # Tạo model
        self.model = self.create_model_with_masking()

        # Callbacks cho training dài hạn với chất lượng cao
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=4,  # Tăng patience=4 để train lâu hơn
                restore_best_weights=True,
                verbose=1,
                min_delta=0.0005  # Giảm min_delta để ít strict hơn
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,  # Giảm learning rate nhẹ hơn
                patience=2,  # Tăng patience lên 2
                min_lr=1e-8,  # Giảm min_lr để train lâu hơn
                verbose=1
            )
        ]

        # Training với explicit device placement
        device_name = '/GPU:0' if len(tf.config.list_physical_devices('GPU')) > 0 else '/CPU:0'
        print_info(f"🚀 Training trên device: {device_name}")

        with tf.device(device_name):
            history = self.model.fit(
                X_train, y_train,
                epochs=epochs,
                batch_size=batch_size,
                validation_data=(X_test, y_test),
                callbacks=callbacks,
                verbose=1
            )

        print_success("Training hoàn thành")
        return history

    def predict_next_draw(self, day_results):
        """
        Dự đoán kỳ tiếp theo từ TẤT CẢ kỳ của ngày

        Parameters:
        day_results: List tất cả kỳ của ngày (≥50 kỳ)

        Returns:
        Dự đoán xác suất cho 80 số
        """
        if len(day_results) < 50:
            print_warning(f"Cần ít nhất 50 kỳ, hiện có {len(day_results)}")
            return None

        print_info(f"Dự đoán từ {len(day_results)} kỳ của ngày")

        # Tạo ma trận nhị phân
        numbers_matrix = np.zeros((len(day_results), 80))
        for i, numbers in enumerate(day_results):
            for num in numbers:
                if 1 <= num <= 80:
                    numbers_matrix[i, num-1] = 1

        # Xử lý sequence length với RIGHT-PADDING
        actual_length = len(numbers_matrix)
        if actual_length <= self.max_sequence_length:
            # Right-pad với zeros (data ở đầu, zeros ở cuối)
            padded_seq = np.zeros((self.max_sequence_length, 80))
            padded_seq[:actual_length] = numbers_matrix
        else:
            # Truncate: lấy max_sequence_length kỳ cuối
            padded_seq = numbers_matrix[-self.max_sequence_length:]

        # Dự đoán với GPU nếu có
        device_name = '/GPU:0' if len(tf.config.list_physical_devices('GPU')) > 0 else '/CPU:0'
        with tf.device(device_name):
            sequence = np.array([padded_seq])
            probabilities = self.model.predict(sequence, verbose=0)[0]

        return probabilities

    def predict_missing_numbers(self, day_results, num_miss=6, use_hybrid=True):
        """
        Dự đoán số trượt kết hợp LSTM và std analysis

        Parameters:
        day_results: Dữ liệu kỳ của ngày
        num_miss: Số lượng số trượt cần dự đoán
        use_hybrid: Có sử dụng hybrid (LSTM + std) không

        Returns:
        dict: Kết quả từ các phương pháp
        """
        if not use_hybrid:
            # Chỉ dùng LSTM
            return self._predict_lstm_only(day_results, num_miss)

        # Hybrid prediction: LSTM + Std Analysis
        print_info("🔄 Hybrid prediction: LSTM + Std Analysis")

        results = {}

        # 1. LSTM Prediction
        print_info("1️⃣ LSTM prediction...")
        lstm_result = self._predict_lstm_only(day_results, num_miss)
        results['lstm'] = lstm_result

        # 2. Std Analysis Prediction
        print_info("2️⃣ Std analysis prediction...")
        std_result = self._predict_std_analysis(num_miss)
        results['std_analysis'] = std_result

        # 3. Ensemble Prediction
        print_info("3️⃣ Ensemble prediction...")
        ensemble_result = self._ensemble_predictions(results, num_miss)
        results['ensemble'] = ensemble_result

        # Hiển thị kết quả
        print_success("✅ Hybrid prediction hoàn thành!")
        print_info("📊 KẾT QUẢ:")
        for method, numbers in results.items():
            if numbers:
                print(f"   • {method.upper()}: {numbers}")

        return results

    def _predict_lstm_only(self, day_results, num_miss):
        """Dự đoán chỉ bằng LSTM"""
        probabilities = self.predict_next_draw(day_results)

        if probabilities is None:
            return []

        # Tạo danh sách (số, xác suất)
        prob_list = [(i + 1, prob) for i, prob in enumerate(probabilities)]
        prob_list.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

        # Lấy num_miss số có xác suất thấp nhất
        miss_numbers = [num for num, _ in prob_list[:num_miss]]
        miss_numbers.sort()

        return miss_numbers

    def _predict_std_analysis(self, num_miss):
        """Dự đoán bằng std analysis từ 30 ngày gần nhất"""
        try:
            # Import RNG analyzer
            from rng_pattern_analyzer import RNGPatternAnalyzer

            analyzer = RNGPatternAnalyzer()
            analyzer.load_data()
            results = analyzer.analyze_30_days_frequency()

            if not results:
                print_warning("Không thể phân tích std")
                return []

            # Lấy số có std thấp và tần suất thấp
            number_stats = results['summary_stats']['number_stats']

            # Tính điểm cho từng số
            scores = []
            for num in range(1, 81):
                stats = number_stats[num]

                # Điểm từ độ ổn định (std thấp = điểm cao)
                stability_score = (1 / (stats['std_percentage'] + 0.001)) * 10

                # Điểm từ tần suất thấp (avg thấp = điểm cao)
                frequency_score = (2.0 - stats['avg_percentage']) * 10

                # Điểm từ số ngày không xuất hiện
                absence_score = stats['days_not_appeared'] * 5

                # Tổng điểm (có thể điều chỉnh trọng số)
                total_score = (stability_score * 0.3 +
                              frequency_score * 0.5 +
                              absence_score * 0.2)

                scores.append((num, total_score))

            # Sắp xếp theo điểm số
            scores.sort(key=lambda x: x[1], reverse=True)
            predicted = [num for num, score in scores[:num_miss]]
            predicted.sort()

            return predicted

        except Exception as e:
            print_warning(f"Lỗi std analysis: {e}")
            return []

    def _ensemble_predictions(self, predictions, num_miss):
        """Tổng hợp dự đoán từ nhiều phương pháp"""
        from collections import defaultdict

        number_scores = defaultdict(float)

        # Trọng số cho từng phương pháp
        weights = {
            'lstm': 0.6,        # LSTM có trọng số cao hơn
            'std_analysis': 0.4  # Std analysis bổ trợ
        }

        for method, numbers in predictions.items():
            if method in weights and numbers:
                weight = weights[method]
                for i, num in enumerate(numbers[:num_miss]):
                    # Điểm số giảm dần theo thứ tự
                    score = weight * (num_miss - i) / num_miss
                    number_scores[num] += score

        # Sắp xếp theo điểm số
        sorted_numbers = sorted(number_scores.items(),
                               key=lambda x: x[1], reverse=True)
        ensemble_result = [num for num, score in sorted_numbers[:num_miss]]
        ensemble_result.sort()

        return ensemble_result

def demo_variable_length():
    """Demo variable length model"""
    print_header("DEMO VARIABLE LENGTH MODEL")

    # Tạo model
    model = VariableLengthKenoModel(max_sequence_length=150)

    # Lấy dữ liệu
    conn = connect_db()
    cursor = conn.cursor(dictionary=True)

    query = """
        SELECT date, results
        FROM histories_keno
        ORDER BY date DESC, time ASC
        LIMIT 5000
    """

    cursor.execute(query)
    rows = cursor.fetchall()
    df = pd.DataFrame(rows)
    df['results'] = df['results'].apply(lambda x: [int(n) for n in x.split(',')])

    cursor.close()
    conn.close()

    # Tạo features
    X, y, lengths = model.create_variable_length_features(df)

    if len(X) > 0:
        print_info(f"Sẵn sàng train với {len(X)} sequences")

        # Train model (demo với ít epochs)
        model.train_variable_model(X, y, lengths, epochs=5)

        # Test prediction với dữ liệu mẫu
        sample_day = []
        for i in range(80):  # 80 kỳ mẫu
            sample_result = list(range(1 + i % 10, 81, 4))[:20]
            sample_day.append(sample_result)

        predictions = model.predict_missing_numbers(sample_day, num_miss=6)
        print_success(f"Demo prediction: {predictions}")

if __name__ == "__main__":
    demo_variable_length()
