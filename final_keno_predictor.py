#!/usr/bin/env python3
"""
Final Keno Predictor - Simplified Version
Chỉ log ra 7 số chính và tạo ngẫu nhiên 10-20 cặp 4 số
"""

import mysql.connector
import random
from datetime import datetime, timedelta
from itertools import combinations
from collections import Counter

def connect_db():
    """Kết nối database"""
    return mysql.connector.connect(
        host='localhost',
        user='root',
        port=3307,
        password='1',
        database='keno'
    )

class FinalKenoPredictor:
    def __init__(self):
        # Money Management Settings
        self.daily_capital = 3_000_000  # 3 triệu VNĐ/ngày
        self.target_profit = 2_000_000  # 2 triệu VNĐ/ngày

        # Dynamic Pricing Settings
        self.base_ticket_price = 10_000     # Giá vé cơ bản 10k VNĐ
        self.min_ticket_price = 10_000      # Giá vé tối thiểu
        self.max_ticket_price = 50_000      # Gi<PERSON> vé tối đa
        self.win_multiplier = 3.2           # Tỷ lệ thắng 3.2x

        # Betting Settings
        self.base_tickets = 5               # Số vé cơ bản mỗi lần
        self.min_tickets = 5                # Tối thiểu 5 vé/lần
        self.max_tickets = 15               # Tối đa 15 vé/lần (tăng để hỗ trợ martingale)
        self.stop_loss_ratio = 0.2          # Dừng khi còn 20% vốn
        self.martingale_trigger = 3         # Bắt đầu martingale từ 3 ván thua

        print("✅ Final Keno Predictor with Dynamic Pricing initialized")
        print(f"💰 Vốn/ngày: {self.daily_capital:,} VNĐ")
        print(f"🎯 Mục tiêu: {self.target_profit:,} VNĐ/ngày")
        print(f"🎫 Giá vé: {self.min_ticket_price:,} - {self.max_ticket_price:,} VNĐ (dynamic)")
        print(f"🏆 Tỷ lệ thắng: {self.win_multiplier}x (chưa trừ chi phí)")
        print(f"📊 Số vé cơ bản: {self.base_tickets} vé/lần")

    def get_day_draws(self, date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results từ string thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            # Fallback: Tạo dữ liệu mẫu để demo
            return self._generate_sample_data()

    def _generate_sample_data(self):
        """Tạo dữ liệu mẫu để demo"""
        sample_data = []
        for i in range(80):  # 80 kì
            # Tạo 20 số ngẫu nhiên từ 1-80
            results = sorted(random.sample(range(1, 81), 20))
            sample_data.append({
                'time': f"08:{i:02d}:00",
                'results': results,
                'period': i + 1
            })
        return sample_data

    def calculate_dynamic_ticket_price(self, consecutive_losses, consecutive_wins, cumulative_profit, recent_big_win=False):
        """Tính giá vé động dựa trên tình hình thắng/thua với chiến thuật mới"""
        base_price = self.base_ticket_price
        price = base_price

        # CHIẾN THUẬT MỚI: Tăng giá vé khi thua liên tiếp
        if consecutive_losses >= 6:
            # Thua 6 kì liên tiếp → tăng giá vé 300%
            price = base_price * 3.0
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng giá vé 200%
            price = base_price * 2.0

        # CHIẾN THUẬT MỚI: Tăng giá vé khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            # Thắng 6 kì liên tiếp → tăng giá vé 300%
            price = base_price * 3.0
        elif consecutive_wins >= 3:
            # Thắng 3 kì liên tiếp → tăng giá vé 200%
            price = base_price * 2.0
        elif recent_big_win:
            # Thắng đậm → tăng 40% để maximize profit
            price = base_price * 1.4

        # Giữ giá cơ bản trong các trường hợp khác
        else:
            price = base_price

        # Cho phép vượt max_ticket_price trong trường hợp martingale hoặc aggressive betting
        max_allowed_price = self.max_ticket_price * 3 if (consecutive_losses >= 3 or consecutive_wins >= 3) else self.max_ticket_price
        return max(self.min_ticket_price, min(price, max_allowed_price))

    def calculate_bet_size(self, current_capital, consecutive_losses, win_rate, ticket_price, consecutive_wins=0):
        """Tính số vé cần mua với chiến thuật mới"""
        # Bắt đầu với base tickets
        base_tickets = self.base_tickets

        # Điều chỉnh dựa trên win rate
        if win_rate >= 0.40:  # Win rate rất cao
            base_tickets = self.base_tickets
        elif win_rate >= 0.35:  # Win rate cao
            base_tickets = max(self.base_tickets - 2, self.min_tickets)
        elif win_rate >= 0.30:  # Win rate trung bình
            base_tickets = max(self.base_tickets - 3, self.min_tickets)
        else:  # Win rate thấp
            base_tickets = self.min_tickets

        # CHIẾN THUẬT MỚI: Tăng cược khi thua liên tiếp
        if consecutive_losses >= 6:
            # Thua 6 kì liên tiếp → tăng 300% (đánh liên tiếp 6 kì)
            base_tickets = int(base_tickets * 3.0)
        elif consecutive_losses >= 3:
            # Thua 3 kì liên tiếp → tăng 200% (đánh liên tiếp 3 kì)
            base_tickets = int(base_tickets * 2.0)

        # CHIẾN THUẬT MỚI: Tăng cược khi thắng liên tiếp (aggressive betting)
        elif consecutive_wins >= 6:
            # Thắng 6 kì liên tiếp → tăng 300% (aggressive betting)
            base_tickets = int(base_tickets * 3.0)
        elif consecutive_wins >= 3:
            # Thắng 3 kì liên tiếp → tăng 200% (aggressive betting)
            base_tickets = int(base_tickets * 2.0)

        # Giới hạn theo vốn còn lại và cho phép vượt max_tickets trong trường hợp martingale
        max_affordable = current_capital // ticket_price
        final_tickets = min(base_tickets, max_affordable, self.max_tickets * 2)  # Cho phép vượt max_tickets

        return max(final_tickets, self.min_tickets) if final_tickets >= self.min_tickets else 0

    def calculate_profit(self, tickets_bought, winning_tickets, ticket_price):
        """Tính lợi nhuận từ một lần chơi với dynamic pricing"""
        cost = tickets_bought * ticket_price
        revenue = winning_tickets * ticket_price * self.win_multiplier
        profit = revenue - cost
        return profit, cost, revenue

    def get_lstm_predictions(self, day_results, num_predictions=10):
        """Dự đoán LSTM dựa trên frequency (fallback)"""
        try:
            # Lấy 5000 kết quả gần nhất
            all_numbers = []
            for result in day_results[-5000:]:
                all_numbers.extend(result['results'])
            
            # Đếm tần suất
            frequency = Counter(all_numbers)
            most_common = frequency.most_common(num_predictions)
            return [num for num, _ in most_common]
        except:
            return list(range(1, num_predictions + 1))

    def get_seasonal_patterns(self, day_results, num_predictions=10):
        """Phân tích seasonal patterns"""
        try:
            # Lấy patterns từ cùng thời điểm trong ngày
            seasonal_numbers = []
            for result in day_results[-1000:]:  # 1000 kết quả gần nhất
                seasonal_numbers.extend(result['results'])
            
            frequency = Counter(seasonal_numbers)
            most_common = frequency.most_common(num_predictions)
            return [num for num, _ in most_common]
        except:
            return list(range(1, num_predictions + 1))

    def get_excluded_numbers(self, day_results):
        """Lấy các số bị loại trừ (consecutive exclusion)"""
        try:
            if not day_results:
                return set()
            
            # Lấy kết quả gần nhất
            recent_results = day_results[-10:]  # 10 kết quả gần nhất
            excluded = set()
            
            for result in recent_results:
                numbers = result['results']
                # Loại trừ số liên tiếp
                for i in range(len(numbers) - 1):
                    if numbers[i+1] - numbers[i] == 1:
                        excluded.add(numbers[i])
                        excluded.add(numbers[i+1])
            
            return excluded
        except:
            return set()

    def predict_period(self, day_draws, period_index):
        """Dự đoán cho một kì cụ thể"""
        if period_index >= len(day_draws):
            return None

        # Lấy dữ liệu training (tất cả kì trước đó)
        training_data = [day_draws[i]['results'] for i in range(period_index)]
        if len(training_data) < 10:
            return None

        # Kết quả thực tế của kì cần dự đoán
        actual_results = day_draws[period_index]['results']
        actual_missing = [i for i in range(1, 81) if i not in actual_results]
        
        # 1. Top 1-3 từ LSTM Original (3 số)
        lstm_predictions = self.get_lstm_predictions(training_data, 10)
        final_7_strategy = lstm_predictions[:3]
        
        # 2. Top 2 từ Seasonal Original (1 số)
        seasonal_predictions = self.get_seasonal_patterns(training_data, 10)
        if len(seasonal_predictions) >= 2:
            final_7_strategy.append(seasonal_predictions[1])  # Top2
        
        # 3. Top 1-3 từ Excluded (3 số)
        excluded_numbers = list(self.get_excluded_numbers(training_data))
        if len(excluded_numbers) >= 3:
            final_7_strategy.extend(excluded_numbers[:3])
        
        # Đảm bảo có đúng 7 số
        while len(final_7_strategy) < 7:
            for num in range(1, 81):
                if num not in final_7_strategy:
                    final_7_strategy.append(num)
                    if len(final_7_strategy) >= 7:
                        break
        
        final_7_strategy = final_7_strategy[:7]
        
        # Tạo ngẫu nhiên 10-20 cặp 4 số từ 7 số
        if len(final_7_strategy) >= 4:
            all_possible_combos = list(combinations(final_7_strategy, 4))
            num_combos = min(random.randint(10, 20), len(all_possible_combos))
            selected_combos = random.sample(all_possible_combos, num_combos)

            # Kiểm tra vé thắng (legacy - để tương thích với code cũ)
            winning_combos = 0
            for combo in selected_combos:
                missing_count = sum(1 for num in combo if num in actual_missing)
                if missing_count >= 4:  # Thắng nếu cả 4 số đều trượt
                    winning_combos += 1

            total_combos = len(selected_combos)
        else:
            winning_combos = 0
            total_combos = 0
            selected_combos = []
        
        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'final_7_strategy': final_7_strategy,
            'selected_combos': selected_combos,
            'winning_combos': winning_combos,
            'total_combos': total_combos
        }

    def test_date_range(self, start_date, end_date, min_periods=50):
        """Test từ ngày start_date đến end_date"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Minimum periods = {min_periods}")
        print("="*60)
        
        # Tạo danh sách ngày test
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # Thống kê tổng hợp
        total_stats = {
            'winning_combos': 0,
            'total_combos': 0,
            'total_predictions': 0,
            'total_days': 0,
            'daily_stats': {},  # Lưu thống kê từng ngày
            'total_profit': 0,
            'total_cost': 0,
            'total_revenue': 0
        }
        
        for test_date in test_dates:
            print(f"\n📅 Testing {test_date}...")
            
            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < min_periods + 1:
                print(f"   ⚠️ Không đủ dữ liệu (có {len(day_draws)}, cần {min_periods + 1})")
                continue
            
            # Test từ kì min_periods+1 đến cuối ngày với money management
            max_period = min(len(day_draws), 119)
            day_predictions = 0
            day_winning = 0
            day_total = 0

            # Money management cho ngày
            current_capital = self.daily_capital
            daily_profit = 0
            daily_cost = 0
            daily_revenue = 0
            consecutive_losses = 0
            consecutive_wins = 0
            total_bets = 0
            recent_big_win = False
            last_period_profit = 0

            # Tính win rate từ historical data (dùng 36% từ test trước)
            estimated_win_rate = 0.36

            for period_index in range(min_periods, max_period):
                # Kiểm tra điều kiện dừng
                if daily_profit >= self.target_profit:
                    print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                    break

                if current_capital <= self.daily_capital * self.stop_loss_ratio:
                    print(f"   🛑 STOP LOSS! Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%) - DỪNG CHƠI")
                    break

                result = self.predict_period(day_draws, period_index)
                if result:
                    # Detect big win (lợi nhuận kì trước > 100k)
                    recent_big_win = last_period_profit > 100_000

                    # Tính giá vé động với logic mới
                    ticket_price = self.calculate_dynamic_ticket_price(consecutive_losses, consecutive_wins, daily_profit, recent_big_win)

                    # Tính số vé cần mua
                    tickets_to_buy = self.calculate_bet_size(current_capital, consecutive_losses, estimated_win_rate, ticket_price, consecutive_wins)

                    if tickets_to_buy == 0:
                        print(f"   💸 Không đủ vốn để mua vé - DỪNG CHƠI")
                        break

                    # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA
                    tickets_to_display = min(tickets_to_buy, len(result['selected_combos']))
                    actual_tickets_bought = result['selected_combos'][:tickets_to_display]

                    winning_tickets = 0
                    winning_details = []
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]

                    for i, combo in enumerate(actual_tickets_bought, 1):
                        missing_count = sum(1 for num in combo if num in actual_missing)
                        is_winning = missing_count >= 4
                        if is_winning:
                            winning_tickets += 1
                        winning_details.append({
                            'ticket_num': i,
                            'combo': combo,
                            'missing_count': missing_count,
                            'is_winning': is_winning
                        })

                    # Tính lợi nhuận dựa trên số vé thực tế mua
                    period_profit, period_cost, period_revenue = self.calculate_profit(tickets_to_display, winning_tickets, ticket_price)

                    # Cập nhật vốn và thống kê
                    current_capital += period_profit
                    daily_profit += period_profit
                    daily_cost += period_cost
                    daily_revenue += period_revenue
                    total_bets += 1

                    # Cập nhật consecutive wins/losses và lưu profit kì này
                    last_period_profit = period_profit
                    if period_profit > 0:
                        consecutive_losses = 0
                        consecutive_wins += 1
                    else:
                        consecutive_losses += 1
                        consecutive_wins = 0

                    day_predictions += 1
                    day_winning += winning_tickets  # Sử dụng số vé thắng thực tế
                    day_total += tickets_to_display  # Sử dụng số vé thực tế mua
                    total_stats['total_predictions'] += 1
                    total_stats['winning_combos'] += winning_tickets  # Sử dụng số vé thắng thực tế
                    total_stats['total_combos'] += tickets_to_display  # Sử dụng số vé thực tế mua
                    total_stats['total_profit'] += period_profit
                    total_stats['total_cost'] += period_cost
                    total_stats['total_revenue'] += period_revenue

                    # Tính target progress
                    target_progress = (daily_profit / self.target_profit) * 100

                    # Log chi tiết với money management
                    self._print_period_details_with_money_v2(result, tickets_to_display, winning_tickets,
                                                        period_profit, current_capital, consecutive_losses,
                                                        daily_profit, target_progress, ticket_price, consecutive_wins,
                                                        winning_details, period_cost, period_revenue)
            
            if day_predictions > 0:
                total_stats['total_days'] += 1
                win_rate = (day_winning / day_total) * 100 if day_total > 0 else 0
                avg_winning_per_period = day_winning / day_predictions if day_predictions > 0 else 0

                # Lưu thống kê ngày
                total_stats['daily_stats'][test_date] = {
                    'predictions': day_predictions,
                    'total_combos': day_total,
                    'winning_combos': day_winning,
                    'win_rate': win_rate,
                    'daily_profit': daily_profit
                }

                print(f"   📊 Ngày {test_date}: {day_predictions} kì, {day_winning}/{day_total} vé thắng ({win_rate:.1f}%)")
                print(f"   📈 Trung bình: {avg_winning_per_period:.1f} vé thắng/kì")

                # Thêm daily summary
                print(f"\n   📊 THỐNG KÊ NGÀY {test_date}:")
                print(f"      • Tổng kì dự đoán: {day_predictions}")
                print(f"      • Tổng vé đánh: {day_total}")
                print(f"      • Tổng vé thắng: {day_winning}")
                print(f"      • Tỷ lệ thắng: {win_rate:.1f}%")
                print(f"      • Trung bình vé thắng/kì: {avg_winning_per_period:.1f}")
                print("   " + "="*50)
        
        # Hiển thị thống kê tổng hợp
        self._display_final_results(total_stats, start_date, end_date)

    def _print_period_details_with_money_v2(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses,
                                        cumulative_profit, target_progress, ticket_price, consecutive_wins,
                                        winning_details=None, period_cost=0, period_revenue=0):
        """In chi tiết kết quả của một kì với money management và lợi nhuận tích lũy"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 7 Strategy (rút gọn)
        print(f"      🎯 Final 7: {' '.join([f'{num:2d}' for num in result['final_7_strategy']])}")

        # Hiển thị các vé 4 số đã mua (chỉ hiển thị 5 vé đầu để không quá dài)
        if winning_details:
            print(f"      🎫 Vé đã mua ({len(winning_details)} vé):")
            for i, detail in enumerate(winning_details[:5], 1):  # Chỉ hiển thị 5 vé đầu
                status_icon = "✅" if detail['is_winning'] else "❌"
                combo_str = ' '.join([f'{num:2d}' for num in sorted(detail['combo'])])
                print(f"         {i}: {combo_str} → {detail['missing_count']}/4 {status_icon}")
            if len(winning_details) > 5:
                print(f"         ... và {len(winning_details) - 5} vé khác")

        # In money management với dynamic pricing
        profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"

        # Hiển thị thông tin martingale/aggressive nếu có
        if consecutive_losses >= 6:
            print(f"      🔥 MARTINGALE 300%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x3 + GIÁ VÉ x3")
        elif consecutive_losses >= 3:
            print(f"      📈 MARTINGALE 200%: Thua {consecutive_losses} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2")
        elif consecutive_wins >= 6:
            print(f"      🚀 AGGRESSIVE 300%: Thắng {consecutive_wins} kì → Tăng SỐ VÉ x3 + GIÁ VÉ x3")
        elif consecutive_wins >= 3:
            print(f"      🔥 AGGRESSIVE 200%: Thắng {consecutive_wins} kì → Tăng SỐ VÉ x2 + GIÁ VÉ x2")

        if period_cost > 0 and period_revenue > 0:
            print(f"      💰 Chi phí: {tickets_bought} vé × {ticket_price:,} = {period_cost:,} VNĐ")
            print(f"      💰 Thu nhập: {winning_tickets} vé × {ticket_price:,} × {self.win_multiplier} = {period_revenue:,} VNĐ")
            print(f"      💰 Lợi nhuận: {profit_icon} {period_profit:+,} VNĐ")
        else:
            print(f"      💰 Mua {tickets_bought} vé @ {ticket_price:,} VNĐ/vé → Thắng {winning_tickets} vé → {profit_icon} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        # Hiển thị thông tin giá vé động với logic mới
        price_status = ""
        if ticket_price > self.base_ticket_price:
            price_increase = ((ticket_price - self.base_ticket_price) / self.base_ticket_price) * 100
            if price_increase >= 200:
                price_status = f"🔥 Tăng giá {price_increase:.0f}% (MARTINGALE 300%)"
            elif price_increase >= 100:
                price_status = f"📈 Tăng giá {price_increase:.0f}% (MARTINGALE 200%)"
            elif price_increase >= 40:
                price_status = f"📈 Tăng giá {price_increase:.0f}% (thắng đậm - aggressive)"
            elif price_increase >= 20:
                if consecutive_wins >= 2:
                    price_status = f"📈 Tăng giá {price_increase:.0f}% (đang lãi - aggressive)"
                else:
                    price_status = f"� Tăng giá {price_increase:.0f}% (thua 3 kì liên tiếp)"
            else:
                price_status = f"📈 Tăng giá {price_increase:.1f}%"
        else:
            price_status = "📊 Giá cơ bản"
        print(f"      🎫 {price_status}")

        # Thêm logs lợi nhuận tích lũy
        cumulative_icon = "🟢" if cumulative_profit > 0 else "🔴" if cumulative_profit < 0 else "🟡"
        print(f"      📈 Lợi nhuận tích lũy: {cumulative_icon} {cumulative_profit:+,} VNĐ")
        print(f"      🎯 Tiến độ mục tiêu: {target_progress:.1f}% ({cumulative_profit:,}/{self.target_profit:,} VNĐ)")

        # Hiển thị còn thiếu bao nhiêu để đạt mục tiêu
        remaining = self.target_profit - cumulative_profit
        if remaining > 0:
            print(f"      📊 Còn thiếu: {remaining:,} VNĐ để đạt mục tiêu")
        else:
            print(f"      🎉 ĐÃ VƯỢT MỤC TIÊU: +{abs(remaining):,} VNĐ")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn)
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"      📊 Prediction: {result['winning_combos']}/{result['total_combos']} ({win_rate:.1f}%)")

    def _print_period_details_with_money(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses):
        """In chi tiết kết quả của một kì với money management"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 7 Strategy (rút gọn)
        print(f"      🎯 Final 7: {' '.join([f'{num:2d}' for num in result['final_7_strategy']])}")

        # In money management
        profit_status = "�" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
        print(f"      💰 Mua {tickets_bought} vé → Thắng {winning_tickets} vé → {profit_status} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn)
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"      📊 Prediction: {result['winning_combos']}/{result['total_combos']} ({win_rate:.1f}%)")

    def _print_period_details(self, result):
        """In chi tiết kết quả của một kì (legacy method)"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # In Final 7 Strategy
        print(f"      🎯 Final 7 Strategy:")
        print(f"         Numbers: {' '.join([f'{num:2d}' for num in result['final_7_strategy']])}")
        print(f"         Source:  T1 T2 T3 S2 E1 E2 E3")
        print(f"         Legend:  LSTM(1-3) + Seasonal(2) + Excluded(1-3)")

        # In thống kê bộ 4 số
        print(f"      🎲 Random 4-number combos:")
        print(f"         Vé thắng: {result['winning_combos']}/{result['total_combos']} vé")
        win_rate = (result['winning_combos'] / result['total_combos']) * 100 if result['total_combos'] > 0 else 0
        print(f"         Tỷ lệ thắng: {win_rate:.1f}%")

    def _display_final_results(self, total_stats, start_date, end_date):
        """Hiển thị kết quả tổng hợp"""
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*60)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")

        # Hiển thị thống kê từng ngày
        if total_stats['daily_stats']:
            print(f"\n📊 THỐNG KÊ TỪNG NGÀY:")
            print("   Ngày        | Kì  | Vé thắng/Tổng vé | Tỷ lệ % | Lợi nhuận")
            print("   " + "-"*62)

            for date, stats in total_stats['daily_stats'].items():
                profit_display = f"{stats['daily_profit']:+,.0f}" if stats['daily_profit'] != 0 else "0"
                print(f"   {date} | {stats['predictions']:2d}  | "
                      f"{stats['winning_combos']:3d}/{stats['total_combos']:3d}        | "
                      f"{stats['win_rate']:5.1f}% | {profit_display:>10}")

        print(f"\n💰 MONEY MANAGEMENT RESULTS:")
        if total_stats['total_cost'] > 0:
            total_roi = (total_stats['total_profit'] / total_stats['total_cost']) * 100
            print(f"   Tổng chi phí: {total_stats['total_cost']:,} VNĐ")
            print(f"   Tổng doanh thu: {total_stats['total_revenue']:,} VNĐ")
            print(f"   Tổng lợi nhuận: {total_stats['total_profit']:+,} VNĐ")
            print(f"   ROI: {total_roi:+.1f}%")
            print(f"   Lợi nhuận/ngày: {total_stats['total_profit']/total_stats['total_days']:+,.0f} VNĐ")

            # Đánh giá mục tiêu
            target_achievement = (total_stats['total_profit']/total_stats['total_days']) / self.target_profit * 100
            if target_achievement >= 100:
                print(f"   🎯 ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")
            else:
                print(f"   ⚠️ CHƯA ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")

        print(f"\n🎲 Trading Performance Overall Stats:")
        if total_stats['total_combos'] > 0:
            win_rate = (total_stats['winning_combos'] / total_stats['total_combos']) * 100
            print(f"   Tổng vé thắng: {total_stats['winning_combos']}")
            print(f"   Tổng vé đã mua: {total_stats['total_combos']}")
            print(f"   Tỷ lệ thắng vé: {win_rate:.1f}%")
            print(f"   Trung bình vé thắng/kì: {total_stats['winning_combos']/total_stats['total_predictions']:.1f}")
            print(f"   Trung bình vé mua/kì: {total_stats['total_combos']/total_stats['total_predictions']:.1f}")

            # Thống kê performance
            if total_stats['daily_stats']:
                daily_rates = [stats['win_rate'] for stats in total_stats['daily_stats'].values()]
                avg_daily_rate = sum(daily_rates) / len(daily_rates)
                max_daily_rate = max(daily_rates)
                min_daily_rate = min(daily_rates)

                print(f"\n📈 Performance Analysis:")
                print(f"   Tỷ lệ thắng trung bình/ngày: {avg_daily_rate:.1f}%")
                print(f"   Tỷ lệ thắng cao nhất: {max_daily_rate:.1f}%")
                print(f"   Tỷ lệ thắng thấp nhất: {min_daily_rate:.1f}%")
        else:
            print(f"   No data available")

        print("="*60)

def test_range(start_date, end_date, min_periods=50):
    """Function để test từ command line"""
    predictor = FinalKenoPredictor()
    return predictor.test_date_range(start_date, end_date, min_periods)

if __name__ == "__main__":
    import sys

    if len(sys.argv) < 3 or len(sys.argv) > 4:
        print("Usage: python final_keno_predictor.py START_DATE END_DATE [MIN_PERIODS]")
        print("Example: python final_keno_predictor.py 2025-04-01 2025-04-30")
        print("Example: python final_keno_predictor.py 2025-04-01 2025-04-30 30")
        sys.exit(1)

    start_date = sys.argv[1]
    end_date = sys.argv[2]
    min_periods = int(sys.argv[3]) if len(sys.argv) == 4 else 50

    test_range(start_date, end_date, min_periods)
